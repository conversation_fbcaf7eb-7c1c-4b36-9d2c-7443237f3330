.violation-popup {
  z-index: 2025;
  max-height: 80vh;
  --popup-close-icon-margin: 24px;
  .violation-content {
    width: 100%;
    background: #ffffff;
    border-radius: 20px 20px 0 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-sizing: border-box;
  }

  .violation-header {
    display: flex;
    align-items: center;
    padding: 24px 20px;
    border-bottom: 1px solid #f0f0f0;
    position: relative;

    .header-icon {
      width: 32px;
      height: 32px;
      margin-right: 12px;

      .icon {
        width: 100%;
        height: 100%;
      }
    }

    .header-title {
      flex: 1;
      font-size: 36px;
      font-weight: 600;
      color: #333;
      line-height: 1;
    }

    .close-btn {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      background: #f5f5f5;
      cursor: pointer;
      transition: background 0.2s ease;

      &:active {
        background: #e0e0e0;
      }

      .close-icon {
        font-size: 30px;
        color: #666;
        line-height: 1;
      }
    }
  }

  .violation-body {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    box-sizing: border-box;
  }

  .info-section {
    margin-bottom: 32px;

    .section-title {
      display: flex;
      align-items: center;
      margin-bottom: 16px;

      // .title-icon {
      //   font-size: 32px;
      //   margin-right: 8px;
      // }

      .title-text {
        font-size: 32px;
        font-weight: 600;
        color: #333;
      }
    }

    .address-content {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 16px;

      .address-text {
        font-size: 28px;
        color: #555;
        line-height: 1.5;
      }
    }

    .count-content {
      .count-item {
        display: flex;
        align-items: baseline;
        background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
        border-radius: 12px;
        padding: 20px;
        color: white;

        .count-label {
          font-size: 28px;
          margin-right: 16px;
        }

        .count-value {
          font-size: 48px;
          font-weight: bold;
          margin-right: 8px;
        }

        .count-unit {
          font-size: 24px;
          opacity: 0.9;
        }
      }
    }

    .violation-list {
      .violation-item {
        display: flex;
        align-items: center;
        background: #ffffff;
        border: 1px solid #e8e8e8;
        border-radius: 12px;
        padding: 16px;
        margin-bottom: 12px;
        transition: all 0.2s ease;

        &:active {
          background: #f8f9fa;
          transform: scale(0.98);
        }

        .item-index {
          width: 32px;
          height: 32px;
          background: #4caf50;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;
          font-weight: bold;
          margin-right: 16px;
          flex-shrink: 0;
        }

        .item-content {
          flex: 1;
          margin-right: 16px;

          .item-text {
            font-size: 28px;
            color: #333;
            line-height: 1.4;
          }
        }

        .item-count {
          display: flex;
          align-items: baseline;
          flex-shrink: 0;

          .count-text {
            font-size: 32px;
            font-weight: bold;
            color: #ff6b6b;
            margin-right: 4px;
          }

          .count-unit {
            font-size: 24px;
            color: #999;
          }
        }
      }

      .more-tip {
        text-align: center;
        padding: 16px;

        .tip-text {
          font-size: 26px;
          color: #999;
          font-style: italic;
        }
      }
    }
  }

  .violation-footer {
    padding: 16px 20px 24px;
    border-top: 1px solid #f0f0f0;

    .footer-btn {
      width: 100%;
      height: 88px;
      background: #fd4925;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;

      &:active {
        transform: scale(0.98);
      }

      .btn-text {
        font-size: 32px;
        font-weight: 600;
        color: white;
      }
    }
  }
}
