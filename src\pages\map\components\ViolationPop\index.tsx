import React, { Component } from "react";
import { View, Text, Image, ScrollView } from "@tarojs/components";
import { Icon, Popup } from "@antmjs/vantui";
import { getImageURL } from "@/util";
import "./index.scss";

interface ViolationItem {
  wfContent: string;
  count: number;
}

interface ViolationData {
  address: string;
  totalCount: number;
  trafficWfCategoryList?: ViolationItem[];
}

interface Props {
  showPop: boolean;
  violationData?: ViolationData;
  onClose: () => void;
}

interface State {}

export default class ViolationPop extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {};
  }

  handleClose = () => {
    this.props.onClose();
  };

  render() {
    const { showPop, violationData } = this.props;

    const {
      address,
      totalCount,
      trafficWfCategoryList = [],
    } = violationData || {};

    return (
      <Popup
        className="violation-popup"
        show={showPop}
        position="bottom"
        zIndex={2025}
        closeable
        round
        onClose={this.handleClose}
      >
        {violationData && (
          <View className="violation-content">
            {/* 头部 */}
            <View className="violation-header">
              <View className="header-icon">
                <Image
                  className="icon"
                  src={getImageURL("FgYmYas6Bd1eSCyJ2YRIuj2VTK1A")}
                  mode="aspectFit"
                />
              </View>
              <View className="header-title">违章信息</View>
              {/* <View className="close-btn" onClick={this.handleClose}>
                <Icon name="cross" className="close-icon" />
              </View> */}
            </View>

            {/* 内容区域 */}
            <ScrollView className="violation-body" scrollY>
              {/* 地址信息 */}
              <View className="info-section">
                <View className="section-title">
                  <Text className="title-text">违章地址</Text>
                </View>
                <View className="address-content">
                  <Text className="address-text">{address}</Text>
                </View>
              </View>

              {/* 统计信息 */}
              <View className="info-section">
                <View className="section-title">
                  <Text className="title-text">违章统计</Text>
                </View>
                <View className="count-content">
                  <View className="count-item">
                    <Text className="count-label">总违章数</Text>
                    <Text className="count-value">{totalCount}</Text>
                    <Text className="count-unit">次</Text>
                  </View>
                </View>
              </View>

              {/* 违章类型列表 */}
              {trafficWfCategoryList && trafficWfCategoryList.length > 0 && (
                <View className="info-section">
                  <View className="section-title">
                    <Text className="title-text">主要违章类型</Text>
                  </View>
                  <View className="violation-list">
                    {trafficWfCategoryList.slice(0, 5).map((item, index) => (
                      <View key={index} className="violation-item">
                        <View className="item-index">{index + 1}</View>
                        <View className="item-content">
                          <Text className="item-text">{item.wfContent}</Text>
                        </View>
                        <View className="item-count">
                          <Text className="count-text">{item.count}</Text>
                          <Text className="count-unit">次</Text>
                        </View>
                      </View>
                    ))}
                    {trafficWfCategoryList.length > 5 && (
                      <View className="more-tip">
                        <Text className="tip-text">
                          还有 {trafficWfCategoryList.length - 5} 种违章类型...
                        </Text>
                      </View>
                    )}
                  </View>
                </View>
              )}
            </ScrollView>

            {/* 底部按钮 */}
            <View className="violation-footer">
              <View className="footer-btn" onClick={this.handleClose}>
                <Text className="btn-text">知道了</Text>
              </View>
            </View>
          </View>
        )}
      </Popup>
    );
  }
}
