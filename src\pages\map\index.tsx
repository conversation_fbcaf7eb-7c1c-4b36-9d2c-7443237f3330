import React, { Component } from "react";
import Taro, { getCurrentInstance } from "@tarojs/taro";
import {
  Map,
  CoverView,
  Button,
  View,
  Text,
  Image,
  MapProps,
} from "@tarojs/components";
import { Icon, Popup } from "@antmjs/vantui";
import {
  pushWebView,
  reverseGeocoder,
  getAudioURL,
  showToast,
  getPageURL,
} from "@/tools";
import { getImageURL } from "@/util";
import { formatRelativeTime } from "@/util/time";
import {
  getTrafficMsgs,
  getSidebarItems,
  getViolationList,
} from "@/api/modules/traffic";
import { doGet } from "@/api/request";
import Audio from "@/pages/live/components/LiveMessage/MessageItem/components/Audio";
import styles from "@/pages/live/components/LiveMessage/MessageItem/index.module.less";
import MarkerPop from "./components/MarkerPop";
import ReportPop from "./components/ReportPop";
import Sidebar from "./components/Sidebar";
import ViolationPop from "./components/ViolationPop";
import "./index.scss";

// 扩展 MapProps.marker 类型，添加 customData 属性
interface ExtendedMarker extends MapProps.marker {
  customData?: any;
}

interface Props {}

interface State {
  showMap: boolean;
  showPopup: boolean;
  showMarkerPopup: boolean;
  mapContext?: any;
  mapMarkers: ExtendedMarker[];
  resourceMarkerData?: any[];
  defaultPosition?: any;
  initMapTimer?: any;
  mapScale: number;
  addMarkerAddress: string;
  currentMarker?: any;
  currentMessage?: any;
  currentAudioUrl: string;
  isIphone: boolean;
  playStatus: boolean;
  markerIcons: string[];
  fetchParams?: any;
  clusterMarkers?: any[];
  violationMarkerIcon: string; // 违章marker图标
  commonMarkerIcon: string; // 通用marker图标
  sidebarItems: any[];
  activeSidebarItemId?: number;
  showViolationPopup: boolean;
  currentViolationData?: any;
}

export default class Index extends Component<Props, State> {
  $instance: any = getCurrentInstance();
  // 创建一个 ref 对象作为类属性
  mapRef: React.RefObject<HTMLDivElement> = React.createRef();
  shouldHandleTap = true; // 是否处理地图点击事件，onMarkerTap会冒泡到onTap，所以定义一个变量阻止冒泡
  currentAudio = ""; // 当前音频url
  audioContext: any = null; // audio 上下文
  canAddMarker = true; // 是否可以添加标记
  lastCenterLocation: any = null; // 记录上次的中心点位置，用于计算距离

  // 计算两点间的直线距离（米，地球赤道半径约6378.137千米）
  calculateDistance(
    lat1: number,
    lng1: number,
    lat2: number,
    lng2: number
  ): number {
    const R = 6378137; // 赤道半径，单位：米，地球赤道半径约6378.137千米
    const dLat = ((lat2 - lat1) * Math.PI) / 180;
    const dLng = ((lng2 - lng1) * Math.PI) / 180;
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos((lat1 * Math.PI) / 180) *
        Math.cos((lat2 * Math.PI) / 180) *
        Math.sin(dLng / 2) *
        Math.sin(dLng / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  // 去重markers，基于id和经纬度判断
  formatMarkersWithDeduplication(
    existingMarkers: ExtendedMarker[],
    newMarkers: ExtendedMarker[]
  ): ExtendedMarker[] {
    const markers: ExtendedMarker[] = [];

    newMarkers.forEach((newMarker) => {
      // 检查是否已存在相同id或相同经纬度的marker（精度到小数点后6位，约1米精度）
      const exists = existingMarkers.some(
        (existing) =>
          existing.id == newMarker.id ||
          (Math.abs(existing.latitude - newMarker.latitude) < 0.000001 &&
            Math.abs(existing.longitude - newMarker.longitude) < 0.000001)
      );

      if (!exists) {
        markers.push(newMarker);
      }
    });

    return markers;
  }

  constructor(props) {
    super(props);
    this.state = {
      showMap: false,
      showPopup: false, // 是否显示去上报弹窗
      showMarkerPopup: false, // 是否显示路况弹窗
      mapContext: null, // 地图上下文
      mapScale: 12, // 地图缩放级别
      mapMarkers: [], // 地图路况标记
      resourceMarkerData: [], // 地图路况标记源数据
      clusterMarkers: [], // 路况标记经纬度相同的集合
      defaultPosition: {
        longitude: 113.681889,
        latitude: 34.763826,
      }, // 默认位置
      initMapTimer: null,
      addMarkerAddress: "", // 添加标记的地址
      currentMarker: null, // 当前标记的位置(经纬度、详细地址)
      currentMessage: null, // 当前标记的路况消息
      playStatus: false,
      currentAudioUrl: "", // 当前播放的音频url
      fetchParams: null, // 请求标记接口参数
      isIphone: false,
      markerIcons: [
        "Fmo8T6lD4zzAdUDFOU3cA7RegK1f",
        "Fv8OVPvy4jxbG7S_Rd5ebb5XJqZZ",
        "Finod3rc9sekJUJtuFT1NaXB0v-g",
        "FolgpiIBMuKMGrvoZP-0D_nTsqag",
        "FjPy1BpE2pF3Il7M7VskV6KSqGfv",
        "FtP5GKB5XjQgC5cAYFXPLeUag1sJ",
        "FoGdMInsv0AflB6e9qc3_sB432P6",
        "FpSGTl7ZHlUfYqDeuQ-QQvm8eGrL",
        "Fs4jJM4nOE8kqEc_lO0yZR1CovA6",
      ],
      // 违章marker图标
      violationMarkerIcon: "FgYmYas6Bd1eSCyJ2YRIuj2VTK1A",
      commonMarkerIcon: "FuEqfWA6U4mEkW_HhRYGaUsBMqAX",
      sidebarItems: [],
      activeSidebarItemId: undefined,
      showViolationPopup: false,
      currentViolationData: null,
    };
  }

  toPage() {
    // pushWebView(this.state.url);
  }
  formatMarkers(list) {
    const { markerIcons } = this.state;
    let markers: any[] = [];

    // 使用对象存储以经纬度为键的Marker数据,将经纬度相同的点合并成一个
    const mergedMarkerDataMap = list.reduce((acc, marker) => {
      const key = `${marker.road.coordinate.y}_${marker.road.coordinate.x}`;

      if (!acc[key]) {
        // 如果该经纬度尚未存储，创建一个新数组
        acc[key] = [marker];
      } else {
        // 否则，将该Marker添加到已有的数组中
        acc[key][0].cluster = true; // 标记为聚合点
        acc[key].push(marker);
      }

      return acc;
    }, {});

    // 提供给Map组件的markers集合
    const mergedMarkerData = Object.values(mergedMarkerDataMap).map(
      (markersArray: any[]) => markersArray[0]
    );
    // 各个相同经纬度marker组成的二维数组
    const groupedMarkerDataArray = Object.values(mergedMarkerDataMap).filter(
      (markersArray: any[]) => markersArray.length > 1
    );
    this.setState({
      clusterMarkers: groupedMarkerDataArray,
      resourceMarkerData: mergedMarkerData,
    });
    mergedMarkerData.map((item, index) => {
      let iconPath = item.type
        ? getImageURL(markerIcons[item.type])
        : getImageURL(markerIcons[0]);
      // 是否存在相同经纬度的marker
      if (item.cluster) {
        iconPath = getImageURL(markerIcons[0]);
      }
      markers.push({
        id: ++index,
        longitude: item.road.coordinate.x,
        latitude: item.road.coordinate.y,
        iconPath,
        width: 45,
        height: 45,
        // clusterId: item.road.coordinate.x + "_" + item.road.coordinate.y,
        // joinCluster: true,
        // customCallout: {
        //   anchorY: 0,
        //   anchorX: 0,
        //   display: "ALWAYS", // ALWAYS BYCLICK onClick={this.onCalloutTap.bind(this)}
        // },
      });
    });
    return markers;
  }
  initMapContext() {
    if (this.mapRef.current) {
      let that = this;
      let mapContext = Taro.createMapContext("trafficMap");
      mapContext.getCenterLocation({
        success: (res) => {
          this.fetchMsgs({
            x: res.longitude,
            y: res.latitude,
          }).then((res) => {
            let mapMarkers = that.formatMarkers(res);
            that.setState({
              mapContext: mapContext,
              mapMarkers: mapMarkers,
            });
          });
        },
        fail: (err) => {},
      });
    } else {
      if (this.state.initMapTimer) clearTimeout(this.state.initMapTimer);
      let timer = setTimeout(() => {
        this.initMapContext();
      }, 300);
      this.setState({
        initMapTimer: timer,
      });
    }
  }
  componentDidMount() {
    let that = this;
    // let mapContext = Taro.createMapContext("trafficMap");
    Taro.getLocation({
      type: "gcj02", // Map组件默认使用的是gcj02坐标系(火星坐标系)，和高德互通
      success: function (res) {
        const latitude = res.latitude;
        const longitude = res.longitude;

        that.setState({
          defaultPosition: {
            longitude,
            latitude,
          },
          showMap: true,
        });
        // 设置默认位置的详细地址
        reverseGeocoder(res).then((geo) => {
          if (geo) {
            that.setState({
              defaultPosition: {
                longitude,
                latitude,
                ...geo,
              },
            });
          }
        });
        that.initMapContext();
      },
      fail: function (res) {
        that.setState({
          showMap: true,
        });
        that.initMapContext();
        Taro.showToast({
          title: "获取位置失败",
          icon: "none",
        });
      },
    });
    // 判断机型
    const model = Taro.getSystemInfoSync().model;
    const isIphone = /iphone/i.test(model);

    const audioContext = Taro.createInnerAudioContext();

    // 保存 InnerAudioContext 到状态
    this.audioContext = audioContext;
    this.setState({
      isIphone,
    });

    // 获取侧边栏数据
    this.fetchSidebarItems();
  }

  fetchSidebarItems() {
    getSidebarItems()
      .then((res) => {
        if (res && Array.isArray(res)) {
          this.setState({
            sidebarItems: res.filter((item) => item.type < 7),
          });
        }
      })
      .catch((err) => {
        console.error("获取侧边栏数据失败:", err);
      });
  }

  handleSidebarItemClick = (item) => {
    const { defaultPosition, activeSidebarItemId } = this.state;

    // 如果点击的是当前激活的项，则恢复原始路况视图
    if (activeSidebarItemId === item.id) {
      this.restoreTrafficMarkers();
      return;
    }

    // 设置当前激活的侧边栏项
    this.setState({
      activeSidebarItemId: item.id,
      mapScale: 12,
    });

    switch (item.type) {
      case 1: // 洗车
      case 2: // 保养
      case 3: // 审车
      case 4: // 美食
        // 请求searchurl获取marker数据更新地图
        if (item.searchurl && defaultPosition) {
          this.fetchSidebarMarkers(item, defaultPosition);
          // 初始化中心点位置
          this.lastCenterLocation = {
            latitude: defaultPosition.latitude,
            longitude: defaultPosition.longitude,
          };
        }
        break;
      case 5: // 违章
        // 请求违章列表接口
        if (defaultPosition) {
          this.fetchViolationData(defaultPosition);
        }
        break;
      case 6: // 外链
        // 跳转到webview页面
        if (item.url) {
          pushWebView(item.url);
        }
        // 外链不需要保持激活状态
        this.setState({
          activeSidebarItemId: undefined,
        });
        break;
      case 7: // 充电站
        // 请求充电站接口获取marker数据更新地图
        if (defaultPosition) {
          this.fetchChargingStationData(defaultPosition);
          // 初始化中心点位置
          this.lastCenterLocation = {
            latitude: defaultPosition.latitude,
            longitude: defaultPosition.longitude,
          };
        }
        break;
      default:
        console.log("未知的侧边栏类型:", item.type);
    }
  };

  restoreTrafficMarkers() {
    const { fetchParams } = this.state;
    this.setState({
      activeSidebarItemId: undefined,
    });

    // 重新获取原始路况数据
    if (fetchParams) {
      this.fetchMsgs(fetchParams).then((res) => {
        const mapMarkers = this.formatMarkers(res);
        this.setState({
          mapMarkers: mapMarkers,
        });
      });
    }
  }

  fetchSidebarMarkers(item, position) {
    if (!item.searchurl) {
      showToast("数据源配置错误");
      return;
    }

    const params = {
      level: this.state.mapScale || 12,
      lat: position.latitude,
      lng: position.longitude,
      pagesize: item.pagesize || 20,
      bounddis: item.bounddis || 5000,
      sid: item.sid || "HN0001",
    };

    // 显示加载提示
    Taro.showLoading({
      title: "加载中...",
    });

    doGet(item.searchurl, params)
      .then((res) => {
        Taro.hideLoading();
        if (res && Array.isArray(res)) {
          // 将获取到的数据转换为地图marker格式
          const sidebarMarkers = this.formatSidebarMarkers(res, item);
          // 只通过setState更新markers，不直接操作地图实例
          this.setState({
            mapMarkers: sidebarMarkers,
          });

          if (res.length === 0) {
            showToast("附近暂无相关信息");
          }
        } else {
          showToast("数据格式错误");
        }
      })
      .catch((err) => {
        Taro.hideLoading();
        console.error("获取侧边栏marker数据失败:", err);
        showToast(String(err.msg));
        // 恢复原始路况视图
        this.restoreTrafficMarkers();
      });
  }

  fetchViolationData(position) {
    const params = {
      lat: position.latitude,
      lng: position.longitude,
    };

    // 显示加载提示
    Taro.showLoading({
      title: "查询违章信息...",
    });
    const { mapContext } = this.state;
    getViolationList(params)
      .then((res) => {
        Taro.hideLoading();
        if (res) {
          // 将违章数据转换为地图marker格式并更新地图
          const violationMarkers = this.formatViolationMarkers(res);
          this.setState({
            mapMarkers: violationMarkers,
          });

          if (violationMarkers.length === 0) {
            showToast("附近暂无违章信息");
          }
        } else {
          showToast("暂无违章信息");
        }
      })
      .catch((err) => {
        console.error("获取违章数据失败:", err);
        Taro.hideLoading();
        showToast(String(err.msg));
        // 重置激活状态
        this.setState({
          activeSidebarItemId: undefined,
        });
      });
  }

  // 获取充电站数据
  fetchChargingStationData(position) {
    const params = {
      distance: 5,
      latitude: position.latitude,
      longitude: position.longitude,
    };

    Taro.showLoading({
      title: "查询充电站信息...",
    });

    const chargingStationUrl =
      "https://dev.jgrm.net/Radio/api/chargingstations/nearby";

    doGet(chargingStationUrl, params)
      .then((res) => {
        Taro.hideLoading();
        if (res && Array.isArray(res)) {
          // 将获取到的数据转换为地图marker格式
          const chargingStationMarkers = this.formatChargingStationMarkers(res);
          // 只通过setState更新markers，不直接操作地图实例
          this.setState({
            mapMarkers: chargingStationMarkers,
          });

          if (res.length === 0) {
            showToast("附近暂无充电站信息");
          }
        } else {
          showToast("数据格式错误");
        }
      })
      .catch((err) => {
        Taro.hideLoading();
        console.error("获取充电站数据失败:", err);
        showToast(String(err.msg || err));
        // 恢复原始路况视图
        this.restoreTrafficMarkers();
      });
  }

  formatSidebarMarkers(data, item): ExtendedMarker[] {
    const { commonMarkerIcon } = this.state;
    // 根据不同的数据源类型格式化marker数据
    return data
      .filter((marker) => marker.lat && marker.lng)
      .map((markerData, index) => {
        return {
          id: index + 1000, // 避免与原有marker id冲突
          longitude: markerData.lng || markerData.longitude,
          latitude: markerData.lat || markerData.latitude,
          iconPath: getImageURL(commonMarkerIcon), // 使用通用marker图标
          width: 45,
          height: 45,
          // clusterId: markerData.lng + "_" + markerData.lat,
          // joinCluster: true,
          // collision: "poi,marker",
          customData: {
            ...markerData,
            sidebarType: item.type,
          },
        };
      });
  }

  // 格式化充电站marker数据
  formatChargingStationMarkers(data): ExtendedMarker[] {
    const { commonMarkerIcon } = this.state;
    // 根据充电站数据格式化marker数据
    return data
      .filter((station) => station.latitude && station.longitude)
      .map((stationData, index) => {
        return {
          id: index + 3000, // 避免与原有marker id冲突，使用3000+
          longitude: stationData.longitude,
          latitude: stationData.latitude,
          iconPath: getImageURL(commonMarkerIcon), // 使用通用marker图标
          width: 45,
          height: 45,
          customData: {
            ...stationData,
            sidebarType: 7, // 充电站类型
            isChargingStation: true,
          },
        };
      });
  }

  formatViolationMarkers(violationDataArray): ExtendedMarker[] {
    const { violationMarkerIcon } = this.state;
    // 将违章数据转换为地图marker格式
    const markers: ExtendedMarker[] = [];

    if (violationDataArray && Array.isArray(violationDataArray)) {
      violationDataArray.forEach((violationData, index) => {
        if (violationData && violationData.lat && violationData.lng) {
          markers.push({
            id: 2000 + index, // 违章marker使用特殊ID，避免重复
            longitude: violationData.lng,
            latitude: violationData.lat,
            iconPath: getImageURL(violationMarkerIcon),
            anchor: { x: 0.5, y: 0.8 }, // 下移锚点使点击区域偏上
            width: 45,
            height: 45,
            label: {
              content: String(violationData.totalCount || 0),
              color: "#ffffff",
              fontSize: 10,
              borderRadius: 10,
              bgColor: "#ff4444",
              padding: 4, // 上下2，左右4
              anchorX: 4,
              anchorY: -44,
              borderWidth: 0,
              borderColor: "transparent",
              textAlign: "right",
            },
            customData: {
              ...violationData,
              isViolation: true,
            },
          });
        }
      });
    }

    return markers;
  }

  showViolationDetails(violationData) {
    // 使用新的违章弹窗组件显示详情
    this.setState({
      showViolationPopup: true,
      currentViolationData: violationData,
    });
  }

  handleViolationPopupClose = () => {
    this.setState({
      showViolationPopup: false,
      currentViolationData: null,
    });
  };

  fetchMsgs(params) {
    this.setState({
      fetchParams: params,
    });
    return getTrafficMsgs(params).then((res) => {
      return res || [];
    });
  }

  refreshMarkers() {
    const { fetchParams } = this.state;
    if (!fetchParams) return;
    const { activeSidebarItemId } = this.state;
    this.fetchMsgs(fetchParams).then((res) => {
      if (activeSidebarItemId === undefined) {
        const mapMarkers = this.formatMarkers(res);
        this.setState({
          mapMarkers: mapMarkers,
        });
      }
    });
  }

  addMarker(location) {
    let { mapContext, markerIcons } = this.state;
    let that = this;
    mapContext.addMarkers({
      markers: [
        {
          id: 202401,
          longitude: location.longitude,
          latitude: location.latitude,
          // iconPath: getImageURL(markerIcons[0]),
          // width: 45,
          // height: 45,
        },
      ],
      success: function (res) {
        console.log("addMarkers success", res);
        reverseGeocoder(location).then((geo) => {
          mapContext.moveToLocation({
            longitude: location.longitude,
            latitude: location.latitude,
          });
          if (geo) {
            that.setState({
              mapScale: 19,
              addMarkerAddress: geo.address,
              currentMarker: {
                ...location,
                ...geo,
              },
            });
          } else {
            that.setState({
              mapScale: 19,
              currentMarker: {
                ...location,
              },
            });
          }
          setTimeout(() => {
            that.setState({
              showPopup: true,
              mapScale: 19.001,
            });
            mapContext.moveToLocation({
              longitude: location.longitude,
              latitude: location.latitude,
            });
          }, 300);
        });
      },
      fail: function (res) {
        console.log("addMarkers fail", res);
      },
      complete: function (res) {
        console.log("addMarkers complete", res);
      },
    });
  }

  removeMarker() {
    let { mapContext } = this.state;
    let that = this;
    mapContext.removeMarkers({
      markerIds: [202401],
      success: function (res) {
        console.log("removeMarkers success", res);
      },
      fail: function (res) {
        console.log("removeMarkers fail", res);
      },
      complete: function () {
        that.canAddMarker = true;
        that.setState({
          currentMarker: null,
        });
      },
    });
  }

  componentWillUnmount() {}

  componentDidShow() {
    this.refreshMarkers();
  }

  componentDidHide() {}
  // 显示路况弹窗
  handleShowMarkerPop(marker) {
    let { mapContext } = this.state;
    let that = this;
    if (marker?.type === 8 && marker.audio) {
      // 语音
      this.onPlayAudio(marker.audio);
    }
    if (marker) {
      let currentMessage = marker || null;
      this.setState({
        currentMessage,
        playStatus: true,
      });

      mapContext.moveToLocation({
        longitude: marker.road.coordinate.x,
        latitude: marker.road.coordinate.y,
      });
      setTimeout(() => {
        this.shouldHandleTap = true;
        this.setState({
          showMarkerPopup: true,
        });
      }, 300);
    }
  }

  onTap(e) {
    const { activeSidebarItemId } = this.state;

    // 只有在展示路况数据时（即activeSidebarItemId为undefined）才处理地图点击事件
    if (activeSidebarItemId !== undefined) {
      return; // 当有侧边栏激活时，禁用地图点击添加marker的功能
    }

    if (!this.shouldHandleTap || !this.canAddMarker) return;
    this.canAddMarker = false;
    const { longitude, latitude } = e.detail;
    this.addMarker({
      longitude,
      latitude,
    });
  }
  onMarkerTap(e) {
    // 阻止事件冒泡
    // 阻止onTap事件冒泡
    let that = this;
    const { markerId } = e.detail;
    let { resourceMarkerData, activeSidebarItemId, sidebarItems, mapMarkers } =
      this.state;

    if (markerId === 202401) return;
    this.shouldHandleTap = false;

    // 根据当前激活的侧边栏类型处理不同的点击逻辑
    if (activeSidebarItemId !== undefined) {
      // 找到当前激活的侧边栏项
      const activeSidebarItem = sidebarItems.find(
        (item) => item.id === activeSidebarItemId
      );

      if (activeSidebarItem) {
        switch (activeSidebarItem.type) {
          case 1: // 洗车
          case 2: // 保养
          case 3: // 审车
          case 4: // 美食
            // 找到被点击的marker数据
            const clickedMarker = mapMarkers?.find(
              (marker) => marker.id === markerId
            );
            if (
              clickedMarker &&
              clickedMarker.customData &&
              clickedMarker.customData.url
            ) {
              // 跳转到webview页面
              Taro.navigateTo({
                url:
                  "/pages/web/index?url=" +
                  encodeURIComponent(clickedMarker.customData.url),
              });
            }
            break;
          case 5: // 违章
            // 找到被点击的违章marker数据
            const violationMarker = mapMarkers?.find(
              (marker) => marker.id === markerId
            );
            if (
              violationMarker &&
              violationMarker.customData &&
              violationMarker.customData.isViolation
            ) {
              this.showViolationDetails(violationMarker.customData);
            }
            break;
          case 6: // 外链
            // 找到被点击的marker数据
            const linkMarker = mapMarkers?.find(
              (marker) => marker.id === markerId
            );
            if (
              linkMarker &&
              linkMarker.customData &&
              linkMarker.customData.url
            ) {
              // 跳转到webview页面
              Taro.navigateTo({
                url:
                  "/pages/web/index?url=" +
                  encodeURIComponent(linkMarker.customData.url),
              });
            }
            break;
          case 7: // 充电站
            // 找到被点击的充电站marker数据
            const chargingStationMarker = mapMarkers?.find(
              (marker) => marker.id === markerId
            );
            if (
              chargingStationMarker &&
              chargingStationMarker.customData &&
              chargingStationMarker.customData.isChargingStation
            ) {
              // 跳转到充电站详情页面
              const stationId = chargingStationMarker.customData.id;
              const url = getPageURL(
                `/actions/app/mcar?#/charging-station/${stationId}`
              );
              pushWebView(url);
            }
            break;
        }
      }

      setTimeout(() => {
        this.shouldHandleTap = true;
      }, 300);
      return;
    }

    // 当没有激活侧边栏时，保持原有的路况marker点击逻辑
    // 获取点击的marker
    let matchedMarker = (resourceMarkerData || []).find(
      (item, index) => index + 1 === markerId
    );
    // 如果点击的是聚合点，跳转到聚合页面
    if (matchedMarker?.cluster) {
      let { clusterMarkers } = this.state;
      let clusters = clusterMarkers?.find((item) => {
        return (
          item.filter((marker) => marker.id === matchedMarker.id).length > 0
        );
      });

      Taro.navigateTo({
        url: "/pages/markers/index",
        events: {
          // 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
          selectMarker: function (selectMarker) {
            that.handleShowMarkerPop(selectMarker);
          },
        },
        success: function (res) {
          // 通过eventChannel向被打开页面传送数据
          res.eventChannel.emit("transData", clusters);
        },
      });
      setTimeout(() => {
        this.shouldHandleTap = true;
      }, 300);
      return;
    }
    this.handleShowMarkerPop(matchedMarker);
  }
  onRegionChange(e: MapProps.onRegionEventDetail) {
    const { type } = e.detail; // end 阶段返回
    const { causedBy } = e; // causedBy，有效值为 drag（拖动导致）、scale（缩放导致）、update（调用更新接口导致）
    const that = this;
    const { activeSidebarItemId, sidebarItems, mapMarkers, mapContext } =
      this.state;

    if (type === "end") {
      if (causedBy === "update") return;
      const { latitude, longitude } = e.detail.centerLocation;

      // 根据当前激活的侧边栏类型处理不同的逻辑
      if (activeSidebarItemId === undefined) {
        // 当activeSidebarItemId为undefined时：请求路况信息接口，保持原有逻辑
        let params = {
          x: longitude,
          y: latitude,
          scale: 2,
        };
        if (e.detail.scale > 10) {
          params.scale = 10;
        } else if (e.detail.scale > 11) {
          params.scale = 5;
        } else if (e.detail.scale > 12) {
          params.scale = 2;
        } else if (e.detail.scale > 13) {
          params.scale = 1;
        }
        that.fetchMsgs(params).then((res) => {
          let mapMarkers = that.formatMarkers(res);
          that.setState({
            mapMarkers: mapMarkers,
          });
        });

        // 更新中心点位置
        this.lastCenterLocation = { latitude, longitude };
      } else {
        // 找到当前激活的侧边栏项
        const activeSidebarItem = sidebarItems.find(
          (item) => item.id === activeSidebarItemId
        );

        if (activeSidebarItem) {
          switch (activeSidebarItem.type) {
            case 1: // 洗车
            case 2: // 保养
            case 3: // 审车
            case 4: // 美食
              // 检查是否需要请求新数据（基于距离判断）
              let shouldFetchData = true;

              if (this.lastCenterLocation && activeSidebarItem.bounddis) {
                const distance = this.calculateDistance(
                  this.lastCenterLocation.latitude,
                  this.lastCenterLocation.longitude,
                  latitude,
                  longitude
                );

                // 如果移动距离小于bounddis，不请求新数据
                if (distance < activeSidebarItem.bounddis) {
                  shouldFetchData = false;
                }
              }

              if (shouldFetchData && activeSidebarItem.searchurl) {
                const params = {
                  level: e.detail.scale || 12,
                  lat: latitude,
                  lng: longitude,
                  pagesize: activeSidebarItem.pagesize || 20,
                  bounddis: activeSidebarItem.bounddis || 5000,
                  sid: activeSidebarItem.sid || "HN0001",
                };

                doGet(activeSidebarItem.searchurl, params)
                  .then((res) => {
                    if (res && Array.isArray(res)) {
                      const newSidebarMarkers = that.formatSidebarMarkers(
                        res,
                        activeSidebarItem
                      );

                      // 将新数据添加到现有markers中（去重）
                      const newMarkers = that.formatMarkersWithDeduplication(
                        mapMarkers || [],
                        newSidebarMarkers
                      );

                      if (newMarkers.length === 0) return;
                      that.setState({
                        mapMarkers: [...mapMarkers, ...newMarkers],
                      });
                    }
                  })
                  .catch((err) => {
                    console.error("地图移动时获取侧边栏数据失败:", err);
                  });

                // 更新中心点位置
                this.lastCenterLocation = { latitude, longitude };
              }
              break;
            case 7: // 充电站
              // 检查是否需要请求新数据（基于距离判断）
              let shouldFetchChargingData = true;

              if (this.lastCenterLocation) {
                const distance = this.calculateDistance(
                  this.lastCenterLocation.latitude,
                  this.lastCenterLocation.longitude,
                  latitude,
                  longitude
                );

                // 如果移动距离小于5000米，不请求新数据
                if (distance < 5000) {
                  shouldFetchChargingData = false;
                }
              }

              if (shouldFetchChargingData) {
                const params = {
                  distance: 5,
                  latitude: latitude,
                  longitude: longitude,
                };

                const chargingStationUrl =
                  "https://dev.jgrm.net/Radio/api/chargingstations/nearby";

                doGet(chargingStationUrl, params)
                  .then((res) => {
                    if (res && Array.isArray(res)) {
                      const newChargingStationMarkers =
                        that.formatChargingStationMarkers(res);

                      // 将新数据添加到现有markers中（去重）
                      const newMarkers = that.formatMarkersWithDeduplication(
                        mapMarkers || [],
                        newChargingStationMarkers
                      );

                      if (newMarkers.length === 0) return;
                      that.setState({
                        mapMarkers: [...mapMarkers, ...newMarkers],
                      });
                    }
                  })
                  .catch((err) => {
                    console.error("地图移动时获取充电站数据失败:", err);
                  });

                // 更新中心点位置
                this.lastCenterLocation = { latitude, longitude };
              }
              break;
            case 5: // 违章
              // 请求违章列表接口
              // const violationParams = {
              //   lat: latitude,
              //   lng: longitude,
              // };

              // getViolationList(violationParams)
              //   .then((res) => {
              //     if (res) {
              //       const violationMarkers = that.formatViolationMarkers(res);
              //       that.setState({
              //         mapMarkers: violationMarkers,
              //       });
              //     }
              //   })
              //   .catch((err) => {
              //     console.error("地图移动时获取违章数据失败:", err);
              //   });
              break;
            case 6: // 外链
            default:
              // 不做任何处理
              break;
          }
        }
      }
    }
  }
  onCalloutTap(e) {}

  onError(e) {}

  toReportPage() {
    let position = this.state.currentMarker || this.state.defaultPosition;
    let that = this;
    Taro.navigateTo({
      url: "/pages/traffic-voice/index",
      success: function (res) {
        // 通过eventChannel向被打开页面传送数据
        res.eventChannel.emit("transData", position);
        that.handleReportPopupClose();
      },
    });
  }

  handleNavigation() {
    Taro.openLocation({
      latitude: this.state.currentMarker.latitude,
      longitude: this.state.currentMarker.longitude,
    });
  }

  handleReportEmit() {
    this.toReportPage();
  }

  handleReportPopupClose() {
    this.setState({
      showPopup: false,
    });
    this.removeMarker();
  }

  handleMarkerPopupClose() {
    this.audioContext && this.audioContext.stop();
    this.currentAudio = "";
    this.setState({
      showMarkerPopup: false,
    });
  }

  onPlayAudio(url) {
    // debugger
    if (url && url === this.currentAudio) {
      if (this.state.playStatus) {
        this.audioContext.pause();
        this.setState({
          currentAudioUrl: "",
          playStatus: false,
        });
      } else {
        this.audioContext.play();
        this.setState({
          currentAudioUrl: url,
          playStatus: true,
        });
      }
    } else {
      this.audioContext && this.audioContext.stop();
      const audioContext = this.audioContext;

      // 修改 InnerAudioContext 的 src 属性
      audioContext.src = getAudioURL(url);

      // 使用 setState 的回调函数确保在状态更新完成后进行操作
      this.audioContext = audioContext;
      // 当音频可以播放就将状态从loading变为可播放
      if (this.state.isIphone) {
        // IOS环境需要立即调用
        this.audioContext.play();
      } else {
        this.audioContext.onCanplay(() => {
          console.log("onCanplay");
          this.audioContext.play();
        });
      }
      // 开始播放后更改图标状态为播放中
      this.audioContext.onPlay(() => {
        console.log("onPlay");
        this.currentAudio = url;
        this.setState({
          currentAudioUrl: url,
          playStatus: true,
        });
      });
      // 暂停后更改图标状态为暂停
      this.audioContext.onPause(() => {
        console.log("onPause");
        this.setState({
          playStatus: false,
        });
      });
      // 播放结束后更改图标状态
      this.audioContext.onEnded(() => {
        // console.log("onEnded");
        this.currentAudio = "";
        this.setState({
          currentAudioUrl: "",
          playStatus: false,
        });
      });
      // 播放出错
      this.audioContext.onError((e) => {
        // currentAudio.current = "";
        // setCurrentAudioUrl('')
      });
    }
  }
  render() {
    let {
      mapMarkers,
      mapScale,
      defaultPosition,
      showMap,
      showPopup,
      addMarkerAddress,
      showMarkerPopup,
      currentMessage,
      playStatus,
      sidebarItems,
      activeSidebarItemId,
      showViolationPopup,
      currentViolationData,
    } = this.state;
    return (
      <View className="map-page">
        {showMap && (
          <>
            <Map
              ref={this.mapRef}
              id="trafficMap"
              className="map-wrap"
              scale={mapScale}
              longitude={defaultPosition.longitude}
              latitude={defaultPosition.latitude}
              enableTraffic
              showScale
              showLocation
              onTap={this.onTap.bind(this)}
              onMarkerTap={this.onMarkerTap.bind(this)}
              onLabelTap={this.onMarkerTap.bind(this)}
              onRegionChange={this.onRegionChange.bind(this)}
              onError={this.onError.bind(this)}
              markers={mapMarkers}
            />
            <View className="report-btn" onClick={this.toReportPage.bind(this)}>
              {/* <View></View> */}
              <View className="">路况上报</View>
            </View>
            {/* 侧边栏 */}
            <Sidebar
              items={sidebarItems}
              activeItemId={activeSidebarItemId}
              onItemClick={this.handleSidebarItemClick}
            />
            {/* 上报弹窗 */}
            <ReportPop
              showPop={showPopup}
              address={addMarkerAddress}
              onNavigation={this.handleNavigation.bind(this)}
              onReport={this.handleReportEmit.bind(this)}
              onClose={this.handleReportPopupClose.bind(this)}
            />
            {/* 路况弹窗 */}
            <MarkerPop
              showPop={showMarkerPopup}
              currentMessage={currentMessage}
              playStatus={playStatus}
              onPlayAudio={this.onPlayAudio.bind(this)}
              onClose={this.handleMarkerPopupClose.bind(this)}
            />
            {/* 违章详情弹窗 */}
            <ViolationPop
              showPop={showViolationPopup}
              violationData={currentViolationData}
              onClose={this.handleViolationPopupClose}
            />
          </>
        )}
      </View>
    );
  }
}
