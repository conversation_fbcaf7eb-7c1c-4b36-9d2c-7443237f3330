# 地图侧边栏组件

## 功能说明

地图侧边栏组件为路况地图页面提供了快速访问不同类型服务的功能，包括洗车、保养、审车、美食、违章查询等。

## 按钮类型说明cursor

### Type 1-4: 地图标记类型

- **Type 1**: 洗车服务
- **Type 2**: 保养服务
- **Type 3**: 审车服务
- **Type 4**: 美食服务

**交互行为**: 点击后请求对应的searchurl接口，获取相关服务的地理位置数据，在地图上显示对应的marker标记，使用通用marker图标(commonMarkerIcon)。

### Type 5: 违章查询

**交互行为**: 点击后请求违章列表接口，在地图上显示违章marker，使用违章专用图标(violationMarkerIcon)，并在marker上显示违章总数。支持多个违章位置同时显示。

### Type 6: 外链跳转

**交互行为**: 点击后跳转到指定的webview页面。

### Type 7: 充电站

**交互行为**: 点击后请求充电站接口，获取附近充电站的地理位置数据，在地图上显示对应的marker标记，使用通用marker图标(commonMarkerIcon)。点击充电站marker跳转到充电站详情页面。

### Type - 其他值暂不处理

## 使用方法

### 1. 基本使用

```tsx
import Sidebar from './components/Sidebar';

<Sidebar
  items={sidebarItems}
  activeItemId={activeSidebarItemId}
  onItemClick={this.handleSidebarItemClick}
/>
```

### 2. 数据格式

侧边栏数据格式参考接口文档 `侧边栏按钮list.md`：

```typescript
interface SidebarItem {
  id: number;
  thumb: string;              // 默认图标
  highLightThumb: string;     // 高亮图标
  keyword: string;            // 按钮名称
  type: number;               // 按钮类型 1-6
  searchurl?: string;         // 数据源URL (type 1-4)
  url?: string;               // 跳转URL (type 6)
  sort: number;               // 排序值
  // ... 其他字段
}
```

### 3. 事件处理

```typescript
handleSidebarItemClick = (item) => {
  switch (item.type) {
    case 1: case 2: case 3: case 4:
      // 获取地图标记数据
      this.fetchSidebarMarkers(item, position);
      break;
    case 5:
      // 获取违章数据
      this.fetchViolationData(position);
      break;
    case 6:
      // 跳转外链
      pushWebView(item.url);
      break;
    case 7:
      // 获取充电站数据
      this.fetchChargingStationData(position);
      break;
  }
};
```

## API接口

### 获取侧边栏数据

- **接口**: `/Radio/traffic/map/right/search/items`
- **方法**: GET
- **参数**: `sid` (站点ID)

### 获取违章数据

- **接口**: `/Radio/traffic/wf/record/list/forapp`
- **方法**: GET
- **参数**: `lat`, `lng`, `v`, `sid`
- **返回**: 违章位置数组，每个位置包含坐标、地址、违章总数等信息

### 获取充电站数据

- **接口**: `https://dev.jgrm.net/Radio/api/chargingstations/nearby`
- **方法**: GET
- **参数**: `distance`, `latitude`, `longitude`
- **返回**: 充电站位置数组，每个充电站包含坐标、名称、ID等信息

## 注意事项

1. 侧边栏数据为空时组件不会渲染
2. 图标加载失败会在控制台输出警告
3. 点击已激活的按钮会恢复原始路况视图
4. 外链类型按钮点击后不保持激活状态
5. 数据加载时会显示loading提示
