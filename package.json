{"name": "jglh-mp", "version": "4.0.0", "private": true, "description": "交广领航小程序", "templateInfo": {"name": "default", "typescript": true, "css": "sass"}, "scripts": {"build:weapp": "taro build --type weapp", "build:weapp:staging": "cross-env NODE_ENV=staging taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:alipay:staging": "cross-env NODE_ENV=staging taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:quickapp": "taro build --type quickapp", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch", "prepare": "husky install"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "dependencies": {"@antmjs/vantui": "^2.5.3", "@babel/runtime": "^7.21.0", "@tarojs/cli": "3.6.34", "@tarojs/components": "3.6.34", "@tarojs/helper": "3.6.34", "@tarojs/plugin-framework-react": "3.6.34", "@tarojs/plugin-inject": "3.6.34", "@tarojs/plugin-platform-alipay": "3.6.34", "@tarojs/plugin-platform-h5": "3.6.34", "@tarojs/plugin-platform-jd": "3.6.34", "@tarojs/plugin-platform-qq": "3.6.34", "@tarojs/plugin-platform-swan": "3.6.34", "@tarojs/plugin-platform-tt": "3.6.34", "@tarojs/plugin-platform-weapp": "3.6.34", "@tarojs/react": "3.6.34", "@tarojs/router": "3.6.34", "@tarojs/runtime": "3.6.34", "@tarojs/shared": "3.6.34", "@tarojs/taro": "3.6.34", "@tarojs/taro-h5": "3.6.34", "@tarojs/taro-rn": "3.6.34", "dayjs": "^1.11.7", "query-string": "^6.14.1", "react": "^18.2.0", "react-dom": "^18.2.0", "taro-ui": "^3.3.0", "umtrack-alipay": "^2.8.0", "umtrack-wx": "^2.8.0", "url-parse": "^1.5.10"}, "devDependencies": {"@babel/core": "^7.21.0", "@commitlint/cli": "^17.4.4", "@commitlint/config-conventional": "^17.4.4", "@commitlint/cz-commitlint": "^17.4.4", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.10", "@tarojs/webpack5-runner": "3.6.34", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@types/webpack-env": "^1.18.0", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "babel-plugin-import": "^1.13.6", "babel-preset-taro": "3.6.34", "commitlint-config-cz": "^0.13.3", "cross-env": "^7.0.3", "cz-conventional-changelog": "^3.3.0", "cz-customizable": "6.9.0", "eslint": "^6.8.0", "eslint-config-taro": "3.6.34", "eslint-plugin-import": "^2.27.5", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^8.0.3", "react-refresh": "^0.14.0", "stylelint": "^16.8.1", "typescript": "^4.9.5", "webpack": "5.73.0"}, "config": {"commitizen": {"path": "node_modules/cz-customizable"}}, "volta": {"node": "18.15.0"}}